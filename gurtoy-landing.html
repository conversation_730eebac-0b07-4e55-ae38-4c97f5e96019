<!doctype html>
<html lang="en-US">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Gurtoy - Premium Ride-On Toys & Kids Vehicles | India's Leading Toy Store</title>
<meta name="description" content="Discover premium ride-on toys, kids vehicles, and outdoor play equipment at Gurtoy. India's trusted toy store offering quality products with free shipping across India." />
<link rel="canonical" href="https://www.thegurtoys.com/" />

<!-- Stylesheets -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Anton&display=swap" rel="stylesheet">

<!-- Framer Motion -->
<script src="https://unpkg.com/framer-motion@10/dist/framer-motion.js"></script>

<style>
/* Gurtoy Custom Styles */
:root {
  --primary-pink: rgb(255,115,185);
  --light-pink: rgb(255,226,245);
  --primary-black: #000000;
  --white: #ffffff;
  --gray-100: #f5f5f5;
  --gray-200: #e5e5e5;
  --gray-800: #374151;
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  line-height: 1.6;
  color: var(--primary-black);
  overflow-x: hidden;
}

/* Header Styles */
.top-header {
  background: var(--primary-pink);
  color: var(--white);
  padding: 8px 0;
  font-size: 14px;
}

.top-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.top-header-left {
  font-weight: 500;
}

.top-header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.top-header-right a {
  color: var(--white);
  text-decoration: none;
  transition: var(--transition);
}

.top-header-right a:hover {
  opacity: 0.8;
}

/* Middle Header - Like Kiddy Zone */
.scroll-header {
  background: var(--white);
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.middle-header {
  padding: 15px 0;
}

.middle-header-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
}

.logo-text-blk img {
  height: 60px;
  width: auto;
}

/* Search Bar */
.middle-header-search {
  flex: 1;
  max-width: 500px;
}

.search-form {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--light-pink);
  border-radius: 25px;
  padding: 5px;
  border: 2px solid var(--primary-pink);
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 10px 15px;
  font-size: 14px;
  outline: none;
  color: var(--primary-black);
}

.search-input::placeholder {
  color: #999;
}

.search-submit {
  background: var(--primary-pink);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.search-submit:hover {
  background: var(--primary-black);
}

.search-submit svg {
  width: 18px;
  height: 18px;
  fill: var(--white);
}

/* Header Actions */
.text-btn {
  display: flex;
  align-items: center;
  gap: 20px;
}

.userinfo a {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--primary-black);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
}

.userinfo a:hover {
  color: var(--primary-pink);
}

.userinfo img {
  width: 20px;
  height: 20px;
}

/* Header Round Buttons */
.header-round-btn {
  display: flex;
  align-items: center;
  gap: 15px;
}

.site-header-cart {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
}

.cart-contents {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background: var(--light-pink);
  border-radius: 50%;
  color: var(--primary-black);
  text-decoration: none;
  transition: var(--transition);
  border: 2px solid var(--primary-pink);
}

.cart-contents:hover {
  background: var(--primary-pink);
  color: var(--white);
}

.cart-contents .count {
  position: absolute;
  top: -5px;
  right: -5px;
  background: var(--primary-pink);
  color: var(--white);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wishlist-btn-header-block a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background: var(--light-pink);
  border-radius: 50%;
  color: var(--primary-black);
  text-decoration: none;
  transition: var(--transition);
  border: 2px solid var(--primary-pink);
}

.wishlist-btn-header-block a:hover {
  background: var(--primary-pink);
  color: var(--white);
}

.wishlist-btn-header-block img {
  width: 20px;
  height: 20px;
  filter: brightness(0);
}

.wishlist-btn-header-block a:hover img {
  filter: brightness(0) invert(1);
}

/* Navigation Menu */
.nav-header {
  background: var(--primary-black);
  padding: 15px 0;
}

.navbar {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 40px;
}

.menu li a {
  color: var(--white);
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  transition: var(--transition);
  position: relative;
  padding: 10px 0;
}

.menu li a:hover {
  color: var(--primary-pink);
}

.menu li a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 5px;
  left: 0;
  background: var(--primary-pink);
  transition: var(--transition);
}

.menu li a:hover::after {
  width: 100%;
}

/* Mobile Menu Toggle */
.mobile-menu {
  display: none;
  color: var(--white);
  font-size: 24px;
  cursor: pointer;
}

/* Hero Section */
.hero-section {
  position: relative;
  height: 60vh;
  overflow: hidden;
}

.hero-slider {
  position: relative;
  height: 100%;
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1.5s ease-in-out;
}

.hero-slide.active {
  opacity: 1;
}

.hero-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0;
}

/* Categories Section */
.categories-section {
  padding: 80px 0;
  background: var(--light-pink);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 50px;
  color: var(--primary-black);
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.category-card {
  background: var(--white);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(255,115,185,0.2);
  transition: var(--transition);
  cursor: pointer;
  border: 2px solid var(--primary-pink);
}

.category-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(255,115,185,0.4);
  border-color: var(--primary-black);
}

.category-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.category-card-content {
  padding: 20px;
  text-align: center;
}

.category-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--primary-black);
}

.category-card p {
  color: var(--gray-800);
  font-size: 0.9rem;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.category-card {
  animation: fadeInUp 0.6s ease-out;
}

.search-submit:hover {
  animation: pulse 0.3s ease-in-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .middle-header-wrapper {
    flex-direction: column;
    gap: 15px;
  }

  .middle-header-search {
    order: 3;
    max-width: 100%;
  }

  .text-btn {
    order: 2;
  }

  .header-round-btn {
    order: 1;
  }

  .menu {
    display: none;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--primary-black);
    padding: 20px;
    gap: 20px;
  }

  .menu.active {
    display: flex;
  }

  .mobile-menu {
    display: block;
  }

  .top-header-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .hero-section {
    height: 40vh;
  }

  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .section-title {
    font-size: 2rem;
  }
}
</style>
</head>

<body>
<div id="page" class="site">
  <!-- Top Header -->
  <div class="top-header">
    <div class="top-header-content">
      <div class="top-header-left">
        <i class="fas fa-shipping-fast"></i> Free Shipping All India
      </div>
      <div class="top-header-right">
        <a href="mailto:<EMAIL>">
          <i class="fas fa-envelope"></i> <EMAIL>
        </a>
        <a href="https://www.google.com/maps/place/Gur+Toy/@30.885705,75.836459,1296m/data=!3m1!1e3!4m6!3m5!1s0x391077ab5a338889:0x6289cc52e63b0e63!8m2!3d30.8855639!4d75.8389345!16s%2Fg%2F11sqk9g69x?authuser=0&entry=ttu&g_ep=EgoyMDI1MDcwOS4wIKXMDSoASAFQAw%3D%3D" target="_blank">
          <i class="fas fa-map-marker-alt"></i> Store Location
        </a>
      </div>
    </div>
  </div>

  <!-- Middle Header - Like Kiddy Zone -->
  <div class="scroll-header">
    <div class="middle-header">
      <div class="middle-header-wrapper">
        <div class="logo-text-blk">
          <a href="#" class="custom-logo-link">
            <img src="logo/GURTOY Registered Trademark Logo (1).png" alt="Gurtoy - Premium Ride-On Toys">
          </a>
        </div>

        <div class="middle-header-search">
          <form class="search-form" role="search" action="#" method="get">
            <input type="search" class="search-input" name="s" value="" placeholder="What are you looking for?" autocomplete="off">
            <button type="submit" class="search-submit" aria-label="Search">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 51.539 51.361">
                <path d="M51.539,49.356L37.247,35.065c3.273-3.74,5.272-8.623,5.272-13.983c0-11.742-9.518-21.26-21.26-21.26 S0,9.339,0,21.082s9.518,21.26,21.26,21.26c5.361,0,10.244-1.999,13.983-5.272l14.292,14.292L51.539,49.356z M2.835,21.082 c0-10.176,8.249-18.425,18.425-18.425s18.425,8.249,18.425,18.425S31.436,39.507,21.26,39.507S2.835,31.258,2.835,21.082z"/>
              </svg>
            </button>
            <input type="hidden" name="post_type" value="product">
          </form>
        </div>

        <div class="text-btn">
          <div class="userinfo">
            <a href="#login">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
              </svg>
              Login
            </a>
          </div>
        </div>

        <div class="header-round-btn">
          <div class="header-round-btn-hover">
            <ul id="site-header-cart" class="site-header-cart menu">
              <li>
                <a class="cart-contents" href="#cart" title="View your shopping cart">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                  </svg>
                  <span class="count">0</span>
                </a>
              </li>
            </ul>
          </div>

          <div class="wishlist-btn-header-block">
            <a href="#wishlist" class="wishlist_products_counter">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
              </svg>
              <span class="wishlist_products_counter_number">0</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation Menu -->
  <div class="nav-header">
    <div class="mobile-menu">
      <i class="fa fa-bars fa-3x js-menu-icon"></i>
    </div>
    <nav class="navbar">
      <ul class="menu">
        <li><a href="#categories"><i class="fa-solid fa-bars"></i> Categories</a></li>
        <li><a href="#summer-fun">Summer Fun</a></li>
        <li><a href="#exclusive">Gurtoy Exclusive</a></li>
        <li><a href="#new-arrivals">New Arrivals</a></li>
      </ul>
    </nav>
  </div>

  <!-- Hero Section -->
  <section class="hero-section">
    <div class="hero-slider">
      <div class="hero-slide active">
        <img src="banner/banner1.png" alt="Gurtoy Premium Ride-On Toys">
      </div>
      <div class="hero-slide">
        <img src="banner/banner2.png" alt="Summer Fun Collection">
      </div>
      <div class="hero-slide">
        <img src="banner/banner3.png" alt="Gurtoy Exclusive">
      </div>
      <div class="hero-slide">
        <img src="banner/banner4.png" alt="New Arrivals">
      </div>
    </div>
  </section>

  <!-- Categories Section -->
  <section id="categories" class="categories-section">
    <h2 class="section-title">Shop by Categories</h2>
    <div class="categories-grid">
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/ff73b9/ffffff?text=Ride-On+Cars" alt="Ride-On Cars">
        <div class="category-card-content">
          <h3>Ride-On Cars</h3>
          <p>Premium electric and manual ride-on cars for kids</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/000000/ffffff?text=Bikes+%26+Scooters" alt="Bikes & Scooters">
        <div class="category-card-content">
          <h3>Bikes & Scooters</h3>
          <p>Balance bikes, scooters, and cycling accessories</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/ff73b9/ffffff?text=Outdoor+Toys" alt="Outdoor Toys">
        <div class="category-card-content">
          <h3>Outdoor Toys</h3>
          <p>Swings, slides, and outdoor play equipment</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/000000/ffffff?text=Electric+Vehicles" alt="Electric Vehicles">
        <div class="category-card-content">
          <h3>Electric Vehicles</h3>
          <p>Battery-powered cars, bikes, and ATVs</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Summer Fun Section -->
  <section id="summer-fun" class="categories-section" style="background: var(--white);">
    <h2 class="section-title">Summer Fun Collection</h2>
    <div class="categories-grid">
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/ff73b9/ffffff?text=Water+Toys" alt="Water Toys">
        <div class="category-card-content">
          <h3>Water Toys</h3>
          <p>Splash pads, water guns, and pool accessories</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/000000/ffffff?text=Beach+Toys" alt="Beach Toys">
        <div class="category-card-content">
          <h3>Beach Toys</h3>
          <p>Sand toys, beach balls, and summer essentials</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/ff73b9/ffffff?text=Garden+Play" alt="Garden Play">
        <div class="category-card-content">
          <h3>Garden Play</h3>
          <p>Trampolines, garden games, and outdoor fun</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Gurtoy Exclusive Section -->
  <section id="exclusive" class="categories-section">
    <h2 class="section-title">Gurtoy Exclusive</h2>
    <div class="categories-grid">
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/000000/ffffff?text=Limited+Edition" alt="Limited Edition">
        <div class="category-card-content">
          <h3>Limited Edition</h3>
          <p>Exclusive designs available only at Gurtoy</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/ff73b9/ffffff?text=Custom+Builds" alt="Custom Builds">
        <div class="category-card-content">
          <h3>Custom Builds</h3>
          <p>Personalized toys built to your specifications</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/000000/ffffff?text=Premium+Collection" alt="Premium Collection">
        <div class="category-card-content">
          <h3>Premium Collection</h3>
          <p>High-end toys with superior quality and features</p>
        </div>
      </div>
    </div>
  </section>

  <!-- New Arrivals Section -->
  <section id="new-arrivals" class="categories-section" style="background: var(--white);">
    <h2 class="section-title">New Arrivals</h2>
    <div class="categories-grid">
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/ff73b9/ffffff?text=Latest+Cars" alt="Latest Cars">
        <div class="category-card-content">
          <h3>Latest Cars</h3>
          <p>Newest models of ride-on cars and vehicles</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/000000/ffffff?text=New+Bikes" alt="New Bikes">
        <div class="category-card-content">
          <h3>New Bikes</h3>
          <p>Fresh arrivals in our bike and scooter collection</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/ff73b9/ffffff?text=Trending+Toys" alt="Trending Toys">
        <div class="category-card-content">
          <h3>Trending Toys</h3>
          <p>Popular toys that kids are loving right now</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer style="background: var(--primary-black); color: var(--white); padding: 60px 0 20px;">
    <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px;">
        <div>
          <img src="logo/GURTOY Registered Trademark Logo (1).png" alt="Gurtoy" style="height: 50px; margin-bottom: 20px; filter: brightness(0) invert(1);">
          <p style="margin-bottom: 20px; line-height: 1.6;">India's leading destination for premium ride-on toys and kids vehicles. Quality, safety, and fun guaranteed.</p>
          <div style="display: flex; gap: 15px;">
            <a href="#" style="color: var(--white); font-size: 20px; transition: var(--transition);"><i class="fab fa-facebook"></i></a>
            <a href="#" style="color: var(--white); font-size: 20px; transition: var(--transition);"><i class="fab fa-instagram"></i></a>
            <a href="#" style="color: var(--white); font-size: 20px; transition: var(--transition);"><i class="fab fa-youtube"></i></a>
            <a href="#" style="color: var(--white); font-size: 20px; transition: var(--transition);"><i class="fab fa-twitter"></i></a>
          </div>
        </div>
        <div>
          <h4 style="margin-bottom: 20px; font-size: 1.2rem;">Quick Links</h4>
          <ul style="list-style: none; padding: 0;">
            <li style="margin-bottom: 10px;"><a href="#categories" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Categories</a></li>
            <li style="margin-bottom: 10px;"><a href="#summer-fun" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Summer Fun</a></li>
            <li style="margin-bottom: 10px;"><a href="#exclusive" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Gurtoy Exclusive</a></li>
            <li style="margin-bottom: 10px;"><a href="#new-arrivals" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">New Arrivals</a></li>
          </ul>
        </div>
        <div>
          <h4 style="margin-bottom: 20px; font-size: 1.2rem;">Contact Info</h4>
          <div style="margin-bottom: 15px;">
            <i class="fas fa-envelope" style="margin-right: 10px; color: var(--primary-red);"></i>
            <a href="mailto:<EMAIL>" style="color: var(--gray-200); text-decoration: none;"><EMAIL></a>
          </div>
          <div style="margin-bottom: 15px;">
            <i class="fas fa-phone" style="margin-right: 10px; color: var(--primary-red);"></i>
            <span style="color: var(--gray-200);">+91 98765 43210</span>
          </div>
          <div style="margin-bottom: 15px;">
            <i class="fas fa-map-marker-alt" style="margin-right: 10px; color: var(--primary-red);"></i>
            <span style="color: var(--gray-200);">Punjab, India</span>
          </div>
        </div>
        <div>
          <h4 style="margin-bottom: 20px; font-size: 1.2rem;">Customer Service</h4>
          <ul style="list-style: none; padding: 0;">
            <li style="margin-bottom: 10px;"><a href="#" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Shipping Info</a></li>
            <li style="margin-bottom: 10px;"><a href="#" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Returns & Exchanges</a></li>
            <li style="margin-bottom: 10px;"><a href="#" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Size Guide</a></li>
            <li style="margin-bottom: 10px;"><a href="#" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">FAQ</a></li>
          </ul>
        </div>
      </div>
      <div style="border-top: 1px solid var(--gray-800); padding-top: 20px; text-align: center;">
        <p style="color: var(--gray-200); margin: 0;">&copy; 2024 Gurtoy. All rights reserved. | Designed with ❤️ for kids</p>
      </div>
    </div>
  </footer>
</div>

<!-- JavaScript -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
<script>
// Hero Slider Functionality
let currentSlide = 0;
const slides = document.querySelectorAll('.hero-slide');
const totalSlides = slides.length;

function showSlide(index) {
  slides.forEach(slide => slide.classList.remove('active'));
  slides[index].classList.add('active');
}

function nextSlide() {
  currentSlide = (currentSlide + 1) % totalSlides;
  showSlide(currentSlide);
}

// Auto-advance slides every 5 seconds
setInterval(nextSlide, 5000);

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  });
});

// Mobile menu toggle
const mobileMenu = document.querySelector('.mobile-menu');
const navbarMenu = document.querySelector('.menu');

if (mobileMenu && navbarMenu) {
  mobileMenu.addEventListener('click', function() {
    navbarMenu.classList.toggle('active');
  });
}

// Add animation on scroll
const observerOptions = {
  threshold: 0.1,
  rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      entry.target.style.opacity = '1';
      entry.target.style.transform = 'translateY(0)';
    }
  });
}, observerOptions);

// Observe all category cards
document.querySelectorAll('.category-card').forEach(card => {
  card.style.opacity = '0';
  card.style.transform = 'translateY(30px)';
  card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
  observer.observe(card);
});

// Add hover effects to footer links
document.querySelectorAll('footer a').forEach(link => {
  link.addEventListener('mouseenter', function() {
    this.style.color = 'var(--primary-red)';
  });
  link.addEventListener('mouseleave', function() {
    this.style.color = 'var(--gray-200)';
  });
});
</script>

</body>
</html>
