<!doctype html>
<html lang="en-US">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Gurtoy - Premium Ride-On Toys & Kids Vehicles | India's Leading Toy Store</title>
<meta name="description" content="Discover premium ride-on toys, kids vehicles, and outdoor play equipment at Gurtoy. India's trusted toy store offering quality products with free shipping across India." />
<link rel="canonical" href="https://www.thegurtoys.com/" />

<!-- Stylesheets -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Anton&display=swap" rel="stylesheet">

<!-- Framer Motion -->
<script src="https://unpkg.com/framer-motion@10/dist/framer-motion.js"></script>

<style>
/* Gurtoy Custom Styles */
:root {
  --primary-red: #dc2626;
  --primary-black: #1a1a1a;
  --accent-purple: #8b5cf6;
  --white: #ffffff;
  --gray-100: #f5f5f5;
  --gray-200: #e5e5e5;
  --gray-800: #374151;
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  line-height: 1.6;
  color: var(--primary-black);
  overflow-x: hidden;
}

/* Header Styles */
.top-header {
  background: var(--primary-red);
  color: var(--white);
  padding: 8px 0;
  font-size: 14px;
}

.top-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.top-header-left {
  font-weight: 500;
}

.top-header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.top-header-right a {
  color: var(--white);
  text-decoration: none;
  transition: var(--transition);
}

.top-header-right a:hover {
  opacity: 0.8;
}

/* Navigation Styles */
.main-nav {
  background: var(--white);
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
}

.logo img {
  height: 60px;
  width: auto;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 40px;
  margin: 0;
}

.nav-menu li a {
  color: var(--primary-black);
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  transition: var(--transition);
  position: relative;
}

.nav-menu li a:hover {
  color: var(--primary-red);
}

.nav-menu li a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -5px;
  left: 0;
  background: var(--primary-red);
  transition: var(--transition);
}

.nav-menu li a:hover::after {
  width: 100%;
}

/* Mobile Menu Toggle */
.mobile-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 24px;
  color: var(--primary-black);
  cursor: pointer;
}

/* Hero Section */
.hero-section {
  position: relative;
  height: 70vh;
  overflow: hidden;
}

.hero-slider {
  position: relative;
  height: 100%;
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1s ease-in-out;
}

.hero-slide.active {
  opacity: 1;
}

.hero-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(220, 38, 38, 0.8), rgba(26, 26, 26, 0.6));
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-content {
  text-align: center;
  color: var(--white);
  max-width: 600px;
  padding: 0 20px;
}

.hero-content h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.cta-button {
  display: inline-block;
  background: var(--primary-red);
  color: var(--white);
  padding: 15px 30px;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 600;
  font-size: 16px;
  transition: var(--transition);
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
}

.cta-button:hover {
  background: var(--primary-black);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
  color: var(--white);
  text-decoration: none;
}

/* Categories Section */
.categories-section {
  padding: 80px 0;
  background: var(--gray-100);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 50px;
  color: var(--primary-black);
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.category-card {
  background: var(--white);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  transition: var(--transition);
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0,0,0,0.2);
}

.category-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.category-card-content {
  padding: 20px;
  text-align: center;
}

.category-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--primary-black);
}

.category-card p {
  color: var(--gray-800);
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }
  
  .mobile-toggle {
    display: block;
  }
  
  .top-header-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  
  .hero-content h1 {
    font-size: 2.5rem;
  }
  
  .hero-content p {
    font-size: 1rem;
  }
  
  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }
}
</style>
</head>

<body>
<div id="page" class="site">
  <!-- Top Header -->
  <div class="top-header">
    <div class="top-header-content">
      <div class="top-header-left">
        <i class="fas fa-shipping-fast"></i> Free Shipping All India
      </div>
      <div class="top-header-right">
        <a href="mailto:<EMAIL>">
          <i class="fas fa-envelope"></i> <EMAIL>
        </a>
        <a href="https://www.google.com/maps/place/Gur+Toy/@30.885705,75.836459,1296m/data=!3m1!1e3!4m6!3m5!1s0x391077ab5a338889:0x6289cc52e63b0e63!8m2!3d30.8855639!4d75.8389345!16s%2Fg%2F11sqk9g69x?authuser=0&entry=ttu&g_ep=EgoyMDI1MDcwOS4wIKXMDSoASAFQAw%3D%3D" target="_blank">
          <i class="fas fa-map-marker-alt"></i> Store Location
        </a>
      </div>
    </div>
  </div>

  <!-- Main Navigation -->
  <nav class="main-nav">
    <div class="nav-content">
      <div class="logo">
        <img src="logo/GURTOY Registered Trademark Logo (1).png" alt="Gurtoy - Premium Ride-On Toys">
      </div>
      
      <ul class="nav-menu">
        <li><a href="#categories">Categories</a></li>
        <li><a href="#summer-fun">Summer Fun</a></li>
        <li><a href="#exclusive">Gurtoy Exclusive</a></li>
        <li><a href="#new-arrivals">New Arrivals</a></li>
      </ul>
      
      <button class="mobile-toggle">
        <i class="fas fa-bars"></i>
      </button>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="hero-section">
    <div class="hero-slider">
      <div class="hero-slide active">
        <img src="banner/banner1.png" alt="Gurtoy Premium Ride-On Toys">
        <div class="hero-overlay">
          <div class="hero-content">
            <h1>Premium Ride-On Toys</h1>
            <p>Discover the joy of adventure with Gurtoy's premium collection of ride-on toys and kids vehicles</p>
            <a href="#categories" class="cta-button">Explore Collection</a>
          </div>
        </div>
      </div>
      <div class="hero-slide">
        <img src="banner/banner2.png" alt="Summer Fun Collection">
        <div class="hero-overlay">
          <div class="hero-content">
            <h1>Summer Fun Collection</h1>
            <p>Beat the heat with our exciting summer toys and outdoor play equipment</p>
            <a href="#summer-fun" class="cta-button">Shop Summer Fun</a>
          </div>
        </div>
      </div>
      <div class="hero-slide">
        <img src="banner/banner3.png" alt="Gurtoy Exclusive">
        <div class="hero-overlay">
          <div class="hero-content">
            <h1>Gurtoy Exclusive</h1>
            <p>Unique designs and premium quality that you won't find anywhere else</p>
            <a href="#exclusive" class="cta-button">View Exclusives</a>
          </div>
        </div>
      </div>
      <div class="hero-slide">
        <img src="banner/banner4.png" alt="New Arrivals">
        <div class="hero-overlay">
          <div class="hero-content">
            <h1>New Arrivals</h1>
            <p>Check out our latest collection of innovative toys and vehicles</p>
            <a href="#new-arrivals" class="cta-button">See New Arrivals</a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Categories Section -->
  <section id="categories" class="categories-section">
    <h2 class="section-title">Shop by Categories</h2>
    <div class="categories-grid">
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/dc2626/ffffff?text=Ride-On+Cars" alt="Ride-On Cars">
        <div class="category-card-content">
          <h3>Ride-On Cars</h3>
          <p>Premium electric and manual ride-on cars for kids</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/8b5cf6/ffffff?text=Bikes+%26+Scooters" alt="Bikes & Scooters">
        <div class="category-card-content">
          <h3>Bikes & Scooters</h3>
          <p>Balance bikes, scooters, and cycling accessories</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/dc2626/ffffff?text=Outdoor+Toys" alt="Outdoor Toys">
        <div class="category-card-content">
          <h3>Outdoor Toys</h3>
          <p>Swings, slides, and outdoor play equipment</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/1a1a1a/ffffff?text=Electric+Vehicles" alt="Electric Vehicles">
        <div class="category-card-content">
          <h3>Electric Vehicles</h3>
          <p>Battery-powered cars, bikes, and ATVs</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Summer Fun Section -->
  <section id="summer-fun" class="categories-section" style="background: var(--white);">
    <h2 class="section-title">Summer Fun Collection</h2>
    <div class="categories-grid">
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/dc2626/ffffff?text=Water+Toys" alt="Water Toys">
        <div class="category-card-content">
          <h3>Water Toys</h3>
          <p>Splash pads, water guns, and pool accessories</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/8b5cf6/ffffff?text=Beach+Toys" alt="Beach Toys">
        <div class="category-card-content">
          <h3>Beach Toys</h3>
          <p>Sand toys, beach balls, and summer essentials</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/dc2626/ffffff?text=Garden+Play" alt="Garden Play">
        <div class="category-card-content">
          <h3>Garden Play</h3>
          <p>Trampolines, garden games, and outdoor fun</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Gurtoy Exclusive Section -->
  <section id="exclusive" class="categories-section">
    <h2 class="section-title">Gurtoy Exclusive</h2>
    <div class="categories-grid">
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/1a1a1a/ffffff?text=Limited+Edition" alt="Limited Edition">
        <div class="category-card-content">
          <h3>Limited Edition</h3>
          <p>Exclusive designs available only at Gurtoy</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/dc2626/ffffff?text=Custom+Builds" alt="Custom Builds">
        <div class="category-card-content">
          <h3>Custom Builds</h3>
          <p>Personalized toys built to your specifications</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/8b5cf6/ffffff?text=Premium+Collection" alt="Premium Collection">
        <div class="category-card-content">
          <h3>Premium Collection</h3>
          <p>High-end toys with superior quality and features</p>
        </div>
      </div>
    </div>
  </section>

  <!-- New Arrivals Section -->
  <section id="new-arrivals" class="categories-section" style="background: var(--white);">
    <h2 class="section-title">New Arrivals</h2>
    <div class="categories-grid">
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/dc2626/ffffff?text=Latest+Cars" alt="Latest Cars">
        <div class="category-card-content">
          <h3>Latest Cars</h3>
          <p>Newest models of ride-on cars and vehicles</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/8b5cf6/ffffff?text=New+Bikes" alt="New Bikes">
        <div class="category-card-content">
          <h3>New Bikes</h3>
          <p>Fresh arrivals in our bike and scooter collection</p>
        </div>
      </div>
      <div class="category-card">
        <img src="https://via.placeholder.com/300x200/1a1a1a/ffffff?text=Trending+Toys" alt="Trending Toys">
        <div class="category-card-content">
          <h3>Trending Toys</h3>
          <p>Popular toys that kids are loving right now</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer style="background: var(--primary-black); color: var(--white); padding: 60px 0 20px;">
    <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px;">
        <div>
          <img src="logo/GURTOY Registered Trademark Logo (1).png" alt="Gurtoy" style="height: 50px; margin-bottom: 20px; filter: brightness(0) invert(1);">
          <p style="margin-bottom: 20px; line-height: 1.6;">India's leading destination for premium ride-on toys and kids vehicles. Quality, safety, and fun guaranteed.</p>
          <div style="display: flex; gap: 15px;">
            <a href="#" style="color: var(--white); font-size: 20px; transition: var(--transition);"><i class="fab fa-facebook"></i></a>
            <a href="#" style="color: var(--white); font-size: 20px; transition: var(--transition);"><i class="fab fa-instagram"></i></a>
            <a href="#" style="color: var(--white); font-size: 20px; transition: var(--transition);"><i class="fab fa-youtube"></i></a>
            <a href="#" style="color: var(--white); font-size: 20px; transition: var(--transition);"><i class="fab fa-twitter"></i></a>
          </div>
        </div>
        <div>
          <h4 style="margin-bottom: 20px; font-size: 1.2rem;">Quick Links</h4>
          <ul style="list-style: none; padding: 0;">
            <li style="margin-bottom: 10px;"><a href="#categories" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Categories</a></li>
            <li style="margin-bottom: 10px;"><a href="#summer-fun" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Summer Fun</a></li>
            <li style="margin-bottom: 10px;"><a href="#exclusive" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Gurtoy Exclusive</a></li>
            <li style="margin-bottom: 10px;"><a href="#new-arrivals" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">New Arrivals</a></li>
          </ul>
        </div>
        <div>
          <h4 style="margin-bottom: 20px; font-size: 1.2rem;">Contact Info</h4>
          <div style="margin-bottom: 15px;">
            <i class="fas fa-envelope" style="margin-right: 10px; color: var(--primary-red);"></i>
            <a href="mailto:<EMAIL>" style="color: var(--gray-200); text-decoration: none;"><EMAIL></a>
          </div>
          <div style="margin-bottom: 15px;">
            <i class="fas fa-phone" style="margin-right: 10px; color: var(--primary-red);"></i>
            <span style="color: var(--gray-200);">+91 98765 43210</span>
          </div>
          <div style="margin-bottom: 15px;">
            <i class="fas fa-map-marker-alt" style="margin-right: 10px; color: var(--primary-red);"></i>
            <span style="color: var(--gray-200);">Punjab, India</span>
          </div>
        </div>
        <div>
          <h4 style="margin-bottom: 20px; font-size: 1.2rem;">Customer Service</h4>
          <ul style="list-style: none; padding: 0;">
            <li style="margin-bottom: 10px;"><a href="#" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Shipping Info</a></li>
            <li style="margin-bottom: 10px;"><a href="#" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Returns & Exchanges</a></li>
            <li style="margin-bottom: 10px;"><a href="#" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Size Guide</a></li>
            <li style="margin-bottom: 10px;"><a href="#" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">FAQ</a></li>
          </ul>
        </div>
      </div>
      <div style="border-top: 1px solid var(--gray-800); padding-top: 20px; text-align: center;">
        <p style="color: var(--gray-200); margin: 0;">&copy; 2024 Gurtoy. All rights reserved. | Designed with ❤️ for kids</p>
      </div>
    </div>
  </footer>
</div>

<!-- JavaScript -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
<script>
// Hero Slider Functionality
let currentSlide = 0;
const slides = document.querySelectorAll('.hero-slide');
const totalSlides = slides.length;

function showSlide(index) {
  slides.forEach(slide => slide.classList.remove('active'));
  slides[index].classList.add('active');
}

function nextSlide() {
  currentSlide = (currentSlide + 1) % totalSlides;
  showSlide(currentSlide);
}

// Auto-advance slides every 5 seconds
setInterval(nextSlide, 5000);

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  });
});

// Mobile menu toggle
const mobileToggle = document.querySelector('.mobile-toggle');
const navMenu = document.querySelector('.nav-menu');

mobileToggle.addEventListener('click', function() {
  navMenu.style.display = navMenu.style.display === 'flex' ? 'none' : 'flex';
});

// Add animation on scroll
const observerOptions = {
  threshold: 0.1,
  rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      entry.target.style.opacity = '1';
      entry.target.style.transform = 'translateY(0)';
    }
  });
}, observerOptions);

// Observe all category cards
document.querySelectorAll('.category-card').forEach(card => {
  card.style.opacity = '0';
  card.style.transform = 'translateY(30px)';
  card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
  observer.observe(card);
});

// Add hover effects to footer links
document.querySelectorAll('footer a').forEach(link => {
  link.addEventListener('mouseenter', function() {
    this.style.color = 'var(--primary-red)';
  });
  link.addEventListener('mouseleave', function() {
    this.style.color = 'var(--gray-200)';
  });
});
</script>

</body>
</html>
