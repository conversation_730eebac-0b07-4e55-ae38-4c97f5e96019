<!doctype html>
<html lang="en-US">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>Gurtoy - Premium Ride-On Toys & Kids Vehicles | India's Leading Toy Store</title>
<meta name="description" content="Discover premium ride-on toys, kids vehicles, and outdoor play equipment at Gurtoy. India's trusted toy store offering quality products with free shipping across India." />
<link rel="canonical" href="https://www.thegurtoys.com/" />

<!-- Stylesheets -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
<link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Anton&display=swap" rel="stylesheet">

<!-- Framer Motion -->
<script src="https://unpkg.com/framer-motion@10/dist/framer-motion.js"></script>

<style>
/* Gurtoy Custom Styles */
:root {
  --primary-pink: rgb(255,115,185);
  --light-pink: rgb(255,226,245);
  --dark-pink: rgb(200,80,150);
  --primary-black: #000000;
  --white: #ffffff;
  --gray-100: #f5f5f5;
  --gray-200: #e5e5e5;
  --gray-800: #374151;
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  line-height: 1.6;
  color: var(--primary-black);
  overflow-x: hidden;
}

/* Header Styles */
.top-header {
  background: var(--primary-pink);
  color: var(--white);
  padding: 8px 0;
  font-size: 14px;
}

.top-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.top-header-left {
  font-weight: 500;
}

.top-header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.top-header-right a {
  color: var(--white);
  text-decoration: none;
  transition: var(--transition);
}

.top-header-right a:hover {
  opacity: 0.8;
}

/* Middle Header - Like Kiddy Zone */
.scroll-header {
  background: var(--white);
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.middle-header {
  padding: 15px 0;
}

.middle-header-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 30px;
}

.logo-text-blk img {
  height: 60px;
  width: auto;
}

/* Search Bar */
.middle-header-search {
  flex: 1;
  max-width: 500px;
}

.search-form {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--light-pink);
  border-radius: 25px;
  padding: 5px;
  border: 2px solid var(--primary-pink);
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 10px 15px;
  font-size: 14px;
  outline: none;
  color: var(--primary-black);
}

.search-input::placeholder {
  color: #999;
}

.search-submit {
  background: var(--primary-pink);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.search-submit:hover {
  background: var(--primary-black);
}

.search-submit svg {
  width: 18px;
  height: 18px;
  fill: var(--white);
}

/* Header Actions */
.text-btn {
  display: flex;
  align-items: center;
  gap: 20px;
}

.userinfo a {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--primary-black);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
}

.userinfo a:hover {
  color: var(--primary-pink);
}

.userinfo img {
  width: 20px;
  height: 20px;
}

/* Header Round Buttons */
.header-round-btn {
  display: flex;
  align-items: center;
  gap: 15px;
}

.site-header-cart {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
}

.cart-contents {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background: var(--light-pink);
  border-radius: 50%;
  color: var(--primary-black);
  text-decoration: none;
  transition: var(--transition);
  border: 2px solid var(--primary-pink);
}

.cart-contents:hover {
  background: var(--primary-pink);
  color: var(--white);
}

.cart-contents .count {
  position: absolute;
  top: -5px;
  right: -5px;
  background: var(--primary-pink);
  color: var(--white);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wishlist-btn-header-block a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  background: var(--light-pink);
  border-radius: 50%;
  color: var(--primary-black);
  text-decoration: none;
  transition: var(--transition);
  border: 2px solid var(--primary-pink);
}

.wishlist-btn-header-block a:hover {
  background: var(--primary-pink);
  color: var(--white);
}

.wishlist-btn-header-block img {
  width: 20px;
  height: 20px;
  filter: brightness(0);
}

.wishlist-btn-header-block a:hover img {
  filter: brightness(0) invert(1);
}

/* Navigation Menu */
.nav-header {
  background: var(--primary-black);
  padding: 15px 0;
}

.navbar {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 40px;
}

.menu li a {
  color: var(--white);
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  transition: var(--transition);
  position: relative;
  padding: 10px 0;
}

.menu li a:hover {
  color: var(--primary-pink);
}

.menu li a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 5px;
  left: 0;
  background: var(--primary-pink);
  transition: var(--transition);
}

.menu li a:hover::after {
  width: 100%;
}

/* Mobile Menu Toggle */
.mobile-menu {
  display: none;
  color: var(--white);
  font-size: 24px;
  cursor: pointer;
}

/* Hero Section */
.hero-section {
  position: relative;
  height: 60vh;
  overflow: hidden;
}

.hero-slider {
  position: relative;
  height: 100%;
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1.5s ease-in-out;
}

.hero-slide.active {
  opacity: 1;
}

.hero-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0;
}

/* Home Categories Section - Like Kiddy Zone */
.home-categories {
  padding: 60px 0;
  background: var(--white);
}

.home-categories h2 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 50px;
  color: var(--primary-black);
}

.home-categories-slider {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  padding: 0 20px;
  scroll-behavior: smooth;
}

.home-categories-each {
  position: relative;
  min-width: 200px;
  height: 250px;
  border-radius: 15px;
  overflow: hidden;
  transition: var(--transition);
  cursor: pointer;
  text-decoration: none;
}

.home-categories-each:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 15px 30px rgba(255,115,185,0.3);
}

.home-categories-each-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-pink), var(--dark-pink));
  border-radius: 15px;
}

.home-categories-each-bg img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 15px;
}

.cat-each-img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: auto;
  z-index: 2;
  transition: var(--transition);
}

.home-categories-each:hover .cat-each-img {
  transform: translate(-50%, -50%) scale(1.1);
}

/* Shop by Brands Section */
.shope-by-brands {
  padding: 80px 0;
  background: var(--light-pink);
}

.shope-by-brands h2 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 50px;
  color: var(--primary-black);
}

.shop-by-brand-slider {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  padding: 0 20px;
  scroll-behavior: smooth;
}

.shop-by-brand-each {
  min-width: 180px;
  height: 240px;
  background: var(--white);
  border-radius: 15px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  cursor: pointer;
  text-decoration: none;
  box-shadow: 0 5px 15px rgba(255,115,185,0.2);
  border: 2px solid var(--primary-pink);
}

.shop-by-brand-each:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(255,115,185,0.4);
  border-color: var(--primary-black);
}

.shop-by-brand-each img {
  width: 100%;
  height: auto;
  max-height: 150px;
  object-fit: contain;
  transition: var(--transition);
}

.shop-by-brand-each:hover img {
  transform: scale(1.1);
}

/* Product Cards Section */
.home-hot-sale {
  padding: 80px 0;
  background: var(--white);
}

.home-hot-sale h2 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 50px;
  color: var(--primary-black);
}

.hot-sale-slider {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  padding: 0 20px;
  scroll-behavior: smooth;
}

.home-hot-sale-each {
  min-width: 280px;
}

.card {
  background: var(--white);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(255,115,185,0.2);
  transition: var(--transition);
  cursor: pointer;
  border: 2px solid var(--primary-pink);
  position: relative;
}

.card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(255,115,185,0.4);
  border-color: var(--primary-black);
}

.card img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: var(--transition);
}

.card:hover img {
  transform: scale(1.05);
}

.card .contents {
  padding: 20px;
}

.card .home-hot-sale-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-black);
  text-decoration: none;
  display: block;
  margin-bottom: 10px;
}

.card .home-hot-sale-title:hover {
  color: var(--primary-pink);
}

.card label {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-pink);
  margin-bottom: 15px;
  display: block;
}

.card .buttons {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
  margin-top: 15px;
}

.card .buttons a {
  background: var(--primary-pink);
  color: var(--white);
  padding: 10px 15px;
  border-radius: 25px;
  text-decoration: none;
  font-size: 0.9rem;
  transition: var(--transition);
  border: none;
  cursor: pointer;
}

.card .buttons a:hover {
  background: var(--dark-pink);
  transform: translateY(-2px);
}

/* Shop by Age Section */
.shop-by-age {
  padding: 80px 0;
  background: var(--light-pink);
}

.shop-by-age h2 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 50px;
  color: var(--primary-black);
}

.age-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

.age-card {
  background: var(--white);
  border-radius: 15px;
  padding: 30px 20px;
  text-align: center;
  transition: var(--transition);
  cursor: pointer;
  border: 2px solid var(--primary-pink);
  box-shadow: 0 5px 15px rgba(255,115,185,0.2);
}

.age-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(255,115,185,0.4);
  border-color: var(--primary-black);
}

.age-card h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-pink);
  margin-bottom: 10px;
}

.age-card p {
  color: var(--gray-800);
  font-size: 0.9rem;
}

/* Shop by Characters Section */
.shop-by-characters {
  padding: 80px 0;
  background: var(--white);
}

.shop-by-characters h2 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 50px;
  color: var(--primary-black);
}

.characters-slider {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  padding: 0 20px;
  scroll-behavior: smooth;
}

.character-card {
  min-width: 200px;
  height: 280px;
  background: var(--white);
  border-radius: 15px;
  overflow: hidden;
  transition: var(--transition);
  cursor: pointer;
  box-shadow: 0 5px 15px rgba(255,115,185,0.2);
  border: 2px solid var(--primary-pink);
}

.character-card:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 15px 30px rgba(255,115,185,0.4);
  border-color: var(--primary-black);
}

.character-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: var(--transition);
}

.character-card:hover img {
  transform: scale(1.1);
}

.character-card-content {
  padding: 15px;
  text-align: center;
}

.character-card h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-black);
}

/* Shop by Gender Section */
.shop-by-gender {
  padding: 80px 0;
  background: var(--light-pink);
}

.shop-by-gender h2 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 50px;
  color: var(--primary-black);
}

.gender-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.gender-card {
  position: relative;
  height: 300px;
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 10px 25px rgba(255,115,185,0.3);
}

.gender-card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: 0 20px 40px rgba(255,115,185,0.5);
}

.gender-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.gender-card:hover img {
  transform: scale(1.1);
}

.gender-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.7));
  color: var(--white);
  padding: 30px 20px 20px;
  text-align: center;
}

.gender-overlay h3 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 10px;
}

.gender-overlay p {
  font-size: 1rem;
  opacity: 0.9;
}

/* Promotional Banners */
.promotional-banners {
  padding: 80px 0;
  background: var(--white);
}

.promotional-banners h2 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 50px;
  color: var(--primary-black);
}

.promo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.promo-banner {
  position: relative;
  height: 200px;
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 10px 25px rgba(255,115,185,0.3);
}

.promo-banner:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(255,115,185,0.5);
}

.promo-banner img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.promo-banner:hover img {
  transform: scale(1.05);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

.search-submit:hover {
  animation: pulse 0.3s ease-in-out;
}

/* Additional Styling */
.span-new {
  position: absolute;
  top: 10px;
  right: 10px;
  background: var(--primary-pink);
  color: var(--white);
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 3;
}

.common-btn:hover {
  background: var(--dark-pink) !important;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255,115,185,0.4);
}

/* Slider Enhancements */
.slider {
  cursor: grab;
  user-select: none;
}

.slider.active {
  cursor: grabbing;
}

.slider::-webkit-scrollbar {
  height: 8px;
}

.slider::-webkit-scrollbar-track {
  background: var(--gray-200);
  border-radius: 10px;
}

.slider::-webkit-scrollbar-thumb {
  background: var(--primary-pink);
  border-radius: 10px;
}

.slider::-webkit-scrollbar-thumb:hover {
  background: var(--dark-pink);
}

/* Section Spacing */
section {
  position: relative;
  overflow: hidden;
}

section::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,115,185,0.1), transparent);
  transition: left 0.5s ease;
}

section:hover::before {
  left: 100%;
}

/* Enhanced Card Styling */
.card .contents {
  position: relative;
  z-index: 2;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,115,185,0.1), rgba(0,0,0,0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.card:hover::before {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .middle-header-wrapper {
    flex-direction: column;
    gap: 15px;
  }

  .middle-header-search {
    order: 3;
    max-width: 100%;
  }

  .text-btn {
    order: 2;
  }

  .header-round-btn {
    order: 1;
  }

  .menu {
    display: none;
    flex-direction: column;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--primary-black);
    padding: 20px;
    gap: 20px;
  }

  .menu.active {
    display: flex;
  }

  .mobile-menu {
    display: block;
  }

  .top-header-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .hero-section {
    height: 40vh;
  }

  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .section-title {
    font-size: 2rem;
  }
}
</style>
</head>

<body>
<div id="page" class="site">
  <!-- Top Header -->
  <div class="top-header">
    <div class="top-header-content">
      <div class="top-header-left">
        <i class="fas fa-shipping-fast"></i> Free Shipping All India
      </div>
      <div class="top-header-right">
        <a href="mailto:<EMAIL>">
          <i class="fas fa-envelope"></i> <EMAIL>
        </a>
        <a href="https://www.google.com/maps/place/Gur+Toy/@30.885705,75.836459,1296m/data=!3m1!1e3!4m6!3m5!1s0x391077ab5a338889:0x6289cc52e63b0e63!8m2!3d30.8855639!4d75.8389345!16s%2Fg%2F11sqk9g69x?authuser=0&entry=ttu&g_ep=EgoyMDI1MDcwOS4wIKXMDSoASAFQAw%3D%3D" target="_blank">
          <i class="fas fa-map-marker-alt"></i> Store Location
        </a>
      </div>
    </div>
  </div>

  <!-- Middle Header - Like Kiddy Zone -->
  <div class="scroll-header">
    <div class="middle-header">
      <div class="middle-header-wrapper">
        <div class="logo-text-blk">
          <a href="#" class="custom-logo-link">
            <img src="logo/GURTOY Registered Trademark Logo (1).png" alt="Gurtoy - Premium Ride-On Toys">
          </a>
        </div>

        <div class="middle-header-search">
          <form class="search-form" role="search" action="#" method="get">
            <input type="search" class="search-input" name="s" value="" placeholder="What are you looking for?" autocomplete="off">
            <button type="submit" class="search-submit" aria-label="Search">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 51.539 51.361">
                <path d="M51.539,49.356L37.247,35.065c3.273-3.74,5.272-8.623,5.272-13.983c0-11.742-9.518-21.26-21.26-21.26 S0,9.339,0,21.082s9.518,21.26,21.26,21.26c5.361,0,10.244-1.999,13.983-5.272l14.292,14.292L51.539,49.356z M2.835,21.082 c0-10.176,8.249-18.425,18.425-18.425s18.425,8.249,18.425,18.425S31.436,39.507,21.26,39.507S2.835,31.258,2.835,21.082z"/>
              </svg>
            </button>
            <input type="hidden" name="post_type" value="product">
          </form>
        </div>

        <div class="text-btn">
          <div class="userinfo">
            <a href="#login">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
              </svg>
              Login
            </a>
          </div>
        </div>

        <div class="header-round-btn">
          <div class="header-round-btn-hover">
            <ul id="site-header-cart" class="site-header-cart menu">
              <li>
                <a class="cart-contents" href="#cart" title="View your shopping cart">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                  </svg>
                  <span class="count">0</span>
                </a>
              </li>
            </ul>
          </div>

          <div class="wishlist-btn-header-block">
            <a href="#wishlist" class="wishlist_products_counter">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
              </svg>
              <span class="wishlist_products_counter_number">0</span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation Menu -->
  <div class="nav-header">
    <div class="mobile-menu">
      <i class="fa fa-bars fa-3x js-menu-icon"></i>
    </div>
    <nav class="navbar">
      <ul class="menu">
        <li><a href="#categories"><i class="fa-solid fa-bars"></i> Categories</a></li>
        <li><a href="#summer-fun">Summer Fun</a></li>
        <li><a href="#exclusive">Gurtoy Exclusive</a></li>
        <li><a href="#new-arrivals">New Arrivals</a></li>
      </ul>
    </nav>
  </div>

  <!-- Hero Section -->
  <section class="hero-section">
    <div class="hero-slider">
      <div class="hero-slide active">
        <img src="banner/banner1.png" alt="Gurtoy Premium Ride-On Toys">
      </div>
      <div class="hero-slide">
        <img src="banner/banner2.png" alt="Summer Fun Collection">
      </div>
      <div class="hero-slide">
        <img src="banner/banner3.png" alt="Gurtoy Exclusive">
      </div>
      <div class="hero-slide">
        <img src="banner/banner4.png" alt="New Arrivals">
      </div>
    </div>
  </section>

  <!-- Categories Section -->
  <section id="categories" class="home-categories">
    <h2>Categories</h2>
    <div class="home-categories-slider responsive slider">
      <a href="#ride-on-cars" class="home-categories-each">
        <img class="cat-each-img" src="https://via.placeholder.com/150x100/ff73b9/ffffff?text=Ride-On">
        <div class="home-categories-each-bg">
          <img src="https://via.placeholder.com/200x250/ff73b9/ffffff?text=BG" alt="">
        </div>
      </a>
      <a href="#electric-bikes" class="home-categories-each">
        <img class="cat-each-img" src="https://via.placeholder.com/150x100/000000/ffffff?text=E-Bikes">
        <div class="home-categories-each-bg">
          <img src="https://via.placeholder.com/200x250/000000/ffffff?text=BG" alt="">
        </div>
      </a>
      <a href="#scooters" class="home-categories-each">
        <img class="cat-each-img" src="https://via.placeholder.com/150x100/ff73b9/ffffff?text=Scooters">
        <div class="home-categories-each-bg">
          <img src="https://via.placeholder.com/200x250/ff73b9/ffffff?text=BG" alt="">
        </div>
      </a>
      <a href="#outdoor" class="home-categories-each">
        <img class="cat-each-img" src="https://via.placeholder.com/150x100/000000/ffffff?text=Outdoor">
        <div class="home-categories-each-bg">
          <img src="https://via.placeholder.com/200x250/000000/ffffff?text=BG" alt="">
        </div>
      </a>
      <a href="#accessories" class="home-categories-each">
        <img class="cat-each-img" src="https://via.placeholder.com/150x100/ff73b9/ffffff?text=Parts">
        <div class="home-categories-each-bg">
          <img src="https://via.placeholder.com/200x250/ff73b9/ffffff?text=BG" alt="">
        </div>
      </a>
    </div>
  </section>

  <!-- Shop by Brands Section -->
  <section class="shope-by-brands">
    <h2>Shop by Brands</h2>
    <div class="shop-by-brand-slider slider">
      <a href="#brand1" class="shop-by-brand-each">
        <img src="https://via.placeholder.com/150x100/ff73b9/ffffff?text=BMW" alt="BMW">
      </a>
      <a href="#brand2" class="shop-by-brand-each">
        <img src="https://via.placeholder.com/150x100/000000/ffffff?text=Mercedes" alt="Mercedes">
      </a>
      <a href="#brand3" class="shop-by-brand-each">
        <img src="https://via.placeholder.com/150x100/ff73b9/ffffff?text=Audi" alt="Audi">
      </a>
      <a href="#brand4" class="shop-by-brand-each">
        <img src="https://via.placeholder.com/150x100/000000/ffffff?text=Ferrari" alt="Ferrari">
      </a>
      <a href="#brand5" class="shop-by-brand-each">
        <img src="https://via.placeholder.com/150x100/ff73b9/ffffff?text=Lamborghini" alt="Lamborghini">
      </a>
      <a href="#brand6" class="shop-by-brand-each">
        <img src="https://via.placeholder.com/150x100/000000/ffffff?text=Porsche" alt="Porsche">
      </a>
    </div>
    <div class="btn-container" style="text-align: center; margin-top: 40px;">
      <a href="#all-brands" class="common-btn" style="background: var(--primary-pink); color: var(--white); padding: 15px 30px; border-radius: 25px; text-decoration: none; font-weight: 600; transition: var(--transition);">
        View All <i class="fa fa-angle-right"></i>
      </a>
    </div>
  </section>

  <!-- Electric Bikes Section -->
  <section id="electric-bikes" class="home-hot-sale cart-anim-block">
    <h2>Electric Bikes</h2>
    <div class="hot-sale-slider slider">
      <div class="home-hot-sale-each">
        <div class="card">
          <a href="#bike1">
            <img src="https://via.placeholder.com/280x250/ff73b9/ffffff?text=Electric+Bike+1" alt="Electric Bike 1">
          </a>
          <div class="contents">
            <a href="#bike1" class="home-hot-sale-title">Premium Electric Bike Model X1</a>
            <label>₹25,999</label>
            <span class="span-new">New</span>
            <div class="buttons list-round-btn">
              <a href="#bike1">View Details</a>
              <a href="#add-to-cart" class="add_to_cart_button">Add to Cart</a>
            </div>
          </div>
        </div>
      </div>
      <div class="home-hot-sale-each">
        <div class="card">
          <a href="#bike2">
            <img src="https://via.placeholder.com/280x250/000000/ffffff?text=Electric+Bike+2" alt="Electric Bike 2">
          </a>
          <div class="contents">
            <a href="#bike2" class="home-hot-sale-title">Sport Electric Bike Pro</a>
            <label>₹32,999</label>
            <span class="span-new">Featured</span>
            <div class="buttons list-round-btn">
              <a href="#bike2">View Details</a>
              <a href="#add-to-cart" class="add_to_cart_button">Add to Cart</a>
            </div>
          </div>
        </div>
      </div>
      <div class="home-hot-sale-each">
        <div class="card">
          <a href="#bike3">
            <img src="https://via.placeholder.com/280x250/ff73b9/ffffff?text=Electric+Bike+3" alt="Electric Bike 3">
          </a>
          <div class="contents">
            <a href="#bike3" class="home-hot-sale-title">Kids Electric Bike Mini</a>
            <label>₹18,999</label>
            <span class="span-new">Popular</span>
            <div class="buttons list-round-btn">
              <a href="#bike3">View Details</a>
              <a href="#add-to-cart" class="add_to_cart_button">Add to Cart</a>
            </div>
          </div>
        </div>
      </div>
      <div class="home-hot-sale-each">
        <div class="card">
          <a href="#bike4">
            <img src="https://via.placeholder.com/280x250/000000/ffffff?text=Electric+Bike+4" alt="Electric Bike 4">
          </a>
          <div class="contents">
            <a href="#bike4" class="home-hot-sale-title">Adventure Electric Bike</a>
            <label>₹45,999</label>
            <span class="span-new">Premium</span>
            <div class="buttons list-round-btn">
              <a href="#bike4">View Details</a>
              <a href="#add-to-cart" class="add_to_cart_button">Add to Cart</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Electric Jeep Section -->
  <section id="electric-jeeps" class="home-hot-sale cart-anim-block" style="background: var(--light-pink);">
    <h2>Electric Jeeps</h2>
    <div class="hot-sale-slider slider">
      <div class="home-hot-sale-each">
        <div class="card">
          <a href="#jeep1">
            <img src="https://via.placeholder.com/280x250/ff73b9/ffffff?text=Electric+Jeep+1" alt="Electric Jeep 1">
          </a>
          <div class="contents">
            <a href="#jeep1" class="home-hot-sale-title">Luxury Electric Jeep Wrangler</a>
            <label>₹85,999</label>
            <span class="span-new">Exclusive</span>
            <div class="buttons list-round-btn">
              <a href="#jeep1">View Details</a>
              <a href="#add-to-cart" class="add_to_cart_button">Add to Cart</a>
            </div>
          </div>
        </div>
      </div>
      <div class="home-hot-sale-each">
        <div class="card">
          <a href="#jeep2">
            <img src="https://via.placeholder.com/280x250/000000/ffffff?text=Electric+Jeep+2" alt="Electric Jeep 2">
          </a>
          <div class="contents">
            <a href="#jeep2" class="home-hot-sale-title">Off-Road Electric Jeep</a>
            <label>₹75,999</label>
            <span class="span-new">Adventure</span>
            <div class="buttons list-round-btn">
              <a href="#jeep2">View Details</a>
              <a href="#add-to-cart" class="add_to_cart_button">Add to Cart</a>
            </div>
          </div>
        </div>
      </div>
      <div class="home-hot-sale-each">
        <div class="card">
          <a href="#jeep3">
            <img src="https://via.placeholder.com/280x250/ff73b9/ffffff?text=Electric+Jeep+3" alt="Electric Jeep 3">
          </a>
          <div class="contents">
            <a href="#jeep3" class="home-hot-sale-title">Kids Electric Jeep Safari</a>
            <label>₹55,999</label>
            <span class="span-new">Kids Special</span>
            <div class="buttons list-round-btn">
              <a href="#jeep3">View Details</a>
              <a href="#add-to-cart" class="add_to_cart_button">Add to Cart</a>
            </div>
          </div>
        </div>
      </div>
      <div class="home-hot-sale-each">
        <div class="card">
          <a href="#jeep4">
            <img src="https://via.placeholder.com/280x250/000000/ffffff?text=Electric+Jeep+4" alt="Electric Jeep 4">
          </a>
          <div class="contents">
            <a href="#jeep4" class="home-hot-sale-title">Premium Electric Jeep Grand</a>
            <label>₹95,999</label>
            <span class="span-new">Luxury</span>
            <div class="buttons list-round-btn">
              <a href="#jeep4">View Details</a>
              <a href="#add-to-cart" class="add_to_cart_button">Add to Cart</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Shop by Age Section -->
  <section class="shop-by-age">
    <h2>Shop by Age</h2>
    <div class="age-grid">
      <div class="age-card">
        <h3>0-2 Years</h3>
        <p>Safe and fun toys for toddlers</p>
      </div>
      <div class="age-card">
        <h3>3-5 Years</h3>
        <p>Educational and interactive toys</p>
      </div>
      <div class="age-card">
        <h3>6-8 Years</h3>
        <p>Adventure and learning toys</p>
      </div>
      <div class="age-card">
        <h3>9-12 Years</h3>
        <p>Advanced and challenging toys</p>
      </div>
      <div class="age-card">
        <h3>13+ Years</h3>
        <p>Teen and adult collectibles</p>
      </div>
    </div>
  </section>

  <!-- Shop by Characters Section -->
  <section class="shop-by-characters">
    <h2>Shop by Characters</h2>
    <div class="characters-slider slider">
      <div class="character-card">
        <img src="https://via.placeholder.com/200x200/ff73b9/ffffff?text=Batman" alt="Batman">
        <div class="character-card-content">
          <h3>Batman</h3>
        </div>
      </div>
      <div class="character-card">
        <img src="https://via.placeholder.com/200x200/000000/ffffff?text=Spider-Man" alt="Spider-Man">
        <div class="character-card-content">
          <h3>Spider-Man</h3>
        </div>
      </div>
      <div class="character-card">
        <img src="https://via.placeholder.com/200x200/ff73b9/ffffff?text=Princess" alt="Princess">
        <div class="character-card-content">
          <h3>Princess</h3>
        </div>
      </div>
      <div class="character-card">
        <img src="https://via.placeholder.com/200x200/000000/ffffff?text=Cars" alt="Cars">
        <div class="character-card-content">
          <h3>Cars</h3>
        </div>
      </div>
      <div class="character-card">
        <img src="https://via.placeholder.com/200x200/ff73b9/ffffff?text=Frozen" alt="Frozen">
        <div class="character-card-content">
          <h3>Frozen</h3>
        </div>
      </div>
    </div>
  </section>

  <!-- Shop by Gender Section -->
  <section class="shop-by-gender">
    <h2>Shop by Gender</h2>
    <div class="gender-grid">
      <a href="#boys" class="gender-card">
        <img src="https://via.placeholder.com/400x300/000000/ffffff?text=Boys+Collection" alt="Boys Collection">
        <div class="gender-overlay">
          <h3>Boys</h3>
          <p>Action-packed toys and vehicles for boys</p>
        </div>
      </a>
      <a href="#girls" class="gender-card">
        <img src="https://via.placeholder.com/400x300/ff73b9/ffffff?text=Girls+Collection" alt="Girls Collection">
        <div class="gender-overlay">
          <h3>Girls</h3>
          <p>Beautiful dolls and creative toys for girls</p>
        </div>
      </a>
    </div>
  </section>

  <!-- Promotional Banners Section -->
  <section class="promotional-banners">
    <h2>Special Offers</h2>
    <div class="promo-grid">
      <a href="#summer-sale" class="promo-banner">
        <img src="https://via.placeholder.com/400x200/ff73b9/ffffff?text=Summer+Sale+50%25+OFF" alt="Summer Sale">
      </a>
      <a href="#new-arrivals" class="promo-banner">
        <img src="https://via.placeholder.com/400x200/000000/ffffff?text=New+Arrivals+2024" alt="New Arrivals">
      </a>
      <a href="#free-shipping" class="promo-banner">
        <img src="https://via.placeholder.com/400x200/ff73b9/ffffff?text=Free+Shipping+All+India" alt="Free Shipping">
      </a>
    </div>
  </section>

  <!-- Footer -->
  <footer style="background: var(--primary-black); color: var(--white); padding: 60px 0 20px;">
    <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 40px; margin-bottom: 40px;">
        <div>
          <img src="logo/GURTOY Registered Trademark Logo (1).png" alt="Gurtoy" style="height: 50px; margin-bottom: 20px; filter: brightness(0) invert(1);">
          <p style="margin-bottom: 20px; line-height: 1.6;">India's leading destination for premium ride-on toys and kids vehicles. Quality, safety, and fun guaranteed.</p>
          <div style="display: flex; gap: 15px;">
            <a href="#" style="color: var(--white); font-size: 20px; transition: var(--transition);"><i class="fab fa-facebook"></i></a>
            <a href="#" style="color: var(--white); font-size: 20px; transition: var(--transition);"><i class="fab fa-instagram"></i></a>
            <a href="#" style="color: var(--white); font-size: 20px; transition: var(--transition);"><i class="fab fa-youtube"></i></a>
            <a href="#" style="color: var(--white); font-size: 20px; transition: var(--transition);"><i class="fab fa-twitter"></i></a>
          </div>
        </div>
        <div>
          <h4 style="margin-bottom: 20px; font-size: 1.2rem;">Quick Links</h4>
          <ul style="list-style: none; padding: 0;">
            <li style="margin-bottom: 10px;"><a href="#categories" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Categories</a></li>
            <li style="margin-bottom: 10px;"><a href="#summer-fun" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Summer Fun</a></li>
            <li style="margin-bottom: 10px;"><a href="#exclusive" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Gurtoy Exclusive</a></li>
            <li style="margin-bottom: 10px;"><a href="#new-arrivals" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">New Arrivals</a></li>
          </ul>
        </div>
        <div>
          <h4 style="margin-bottom: 20px; font-size: 1.2rem;">Contact Info</h4>
          <div style="margin-bottom: 15px;">
            <i class="fas fa-envelope" style="margin-right: 10px; color: var(--primary-red);"></i>
            <a href="mailto:<EMAIL>" style="color: var(--gray-200); text-decoration: none;"><EMAIL></a>
          </div>
          <div style="margin-bottom: 15px;">
            <i class="fas fa-phone" style="margin-right: 10px; color: var(--primary-red);"></i>
            <span style="color: var(--gray-200);">+91 98765 43210</span>
          </div>
          <div style="margin-bottom: 15px;">
            <i class="fas fa-map-marker-alt" style="margin-right: 10px; color: var(--primary-red);"></i>
            <span style="color: var(--gray-200);">Punjab, India</span>
          </div>
        </div>
        <div>
          <h4 style="margin-bottom: 20px; font-size: 1.2rem;">Customer Service</h4>
          <ul style="list-style: none; padding: 0;">
            <li style="margin-bottom: 10px;"><a href="#" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Shipping Info</a></li>
            <li style="margin-bottom: 10px;"><a href="#" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Returns & Exchanges</a></li>
            <li style="margin-bottom: 10px;"><a href="#" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">Size Guide</a></li>
            <li style="margin-bottom: 10px;"><a href="#" style="color: var(--gray-200); text-decoration: none; transition: var(--transition);">FAQ</a></li>
          </ul>
        </div>
      </div>
      <div style="border-top: 1px solid var(--gray-800); padding-top: 20px; text-align: center;">
        <p style="color: var(--gray-200); margin: 0;">&copy; 2024 Gurtoy. All rights reserved. | Designed with ❤️ for kids</p>
      </div>
    </div>
  </footer>
</div>

<!-- JavaScript -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
<script>
// Hero Slider Functionality
let currentSlide = 0;
const slides = document.querySelectorAll('.hero-slide');
const totalSlides = slides.length;

function showSlide(index) {
  slides.forEach(slide => slide.classList.remove('active'));
  slides[index].classList.add('active');
}

function nextSlide() {
  currentSlide = (currentSlide + 1) % totalSlides;
  showSlide(currentSlide);
}

// Auto-advance slides every 5 seconds
setInterval(nextSlide, 5000);

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  });
});

// Mobile menu toggle
const mobileMenu = document.querySelector('.mobile-menu');
const navbarMenu = document.querySelector('.menu');

if (mobileMenu && navbarMenu) {
  mobileMenu.addEventListener('click', function() {
    navbarMenu.classList.toggle('active');
  });
}

// Enhanced Animation on Scroll - Like Kiddy Zone
const observerOptions = {
  threshold: 0.1,
  rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      entry.target.style.opacity = '1';
      entry.target.style.transform = 'translateY(0)';
      entry.target.classList.add('fade-in-up');
    }
  });
}, observerOptions);

// Observe all animated elements
const animatedElements = [
  '.home-categories-each',
  '.shop-by-brand-each',
  '.card',
  '.age-card',
  '.character-card',
  '.gender-card',
  '.promo-banner'
];

animatedElements.forEach(selector => {
  document.querySelectorAll(selector).forEach(element => {
    element.style.opacity = '0';
    element.style.transform = 'translateY(30px)';
    element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    observer.observe(element);
  });
});

// Horizontal scroll for sliders
function initializeSliders() {
  const sliders = document.querySelectorAll('.slider');

  sliders.forEach(slider => {
    let isDown = false;
    let startX;
    let scrollLeft;

    slider.addEventListener('mousedown', (e) => {
      isDown = true;
      slider.classList.add('active');
      startX = e.pageX - slider.offsetLeft;
      scrollLeft = slider.scrollLeft;
    });

    slider.addEventListener('mouseleave', () => {
      isDown = false;
      slider.classList.remove('active');
    });

    slider.addEventListener('mouseup', () => {
      isDown = false;
      slider.classList.remove('active');
    });

    slider.addEventListener('mousemove', (e) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.pageX - slider.offsetLeft;
      const walk = (x - startX) * 2;
      slider.scrollLeft = scrollLeft - walk;
    });
  });
}

// Card hover animations
function initializeCardAnimations() {
  document.querySelectorAll('.card').forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-15px) scale(1.02)';
      this.style.boxShadow = '0 20px 40px rgba(255,115,185,0.5)';
    });

    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0) scale(1)';
      this.style.boxShadow = '0 5px 20px rgba(255,115,185,0.2)';
    });
  });
}

// Staggered animations for sections
function staggeredAnimation(selector, delay = 100) {
  const elements = document.querySelectorAll(selector);
  elements.forEach((element, index) => {
    setTimeout(() => {
      element.style.opacity = '1';
      element.style.transform = 'translateY(0)';
    }, index * delay);
  });
}

// Initialize all animations
document.addEventListener('DOMContentLoaded', function() {
  initializeSliders();
  initializeCardAnimations();

  // Staggered animations for different sections
  setTimeout(() => staggeredAnimation('.home-categories-each', 150), 500);
  setTimeout(() => staggeredAnimation('.shop-by-brand-each', 100), 1000);
  setTimeout(() => staggeredAnimation('.card', 200), 1500);
});

// Add hover effects to footer links
document.querySelectorAll('footer a').forEach(link => {
  link.addEventListener('mouseenter', function() {
    this.style.color = 'var(--primary-pink)';
  });
  link.addEventListener('mouseleave', function() {
    this.style.color = 'var(--gray-200)';
  });
});

// Smooth scroll behavior for navigation
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  });
});
</script>

</body>
</html>
